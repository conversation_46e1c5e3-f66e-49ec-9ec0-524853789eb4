2025-09-22 15:08:14,921 - INFO - PDF处理测试服务启动中...
2025-09-22 15:08:14,922 - INFO - 测试服务启动完成，监听端口 18052
2025-09-22 15:08:15,078 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:18052
 * Running on http://************:18052
2025-09-22 15:08:15,079 - INFO - [33mPress CTRL+C to quit[0m
2025-09-22 15:08:15,082 - INFO -  * Restarting with stat
2025-09-22 15:08:16,161 - INFO - PDF处理测试服务启动中...
2025-09-22 15:08:16,162 - INFO - 测试服务启动完成，监听端口 18052
2025-09-22 15:08:16,199 - WARNING -  * Debugger is active!
2025-09-22 15:08:16,208 - INFO -  * Debugger PIN: 503-295-468
2025-09-22 15:08:37,396 - INFO - 127.0.0.1 - - [22/Sep/2025 15:08:37] "GET / HTTP/1.1" 200 -
2025-09-22 15:08:39,461 - INFO - 开始上传文件或文字
2025-09-22 15:08:39,462 - INFO - 接收到参数: {'type': 2, 'url': 'http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf', 'imageName': 'AWS_Certified_Solutions_Architect_Professional.pdf'}
2025-09-22 15:08:39,462 - INFO - 检测文件类型: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:08:39,463 - INFO - 文件扩展名: .pdf
2025-09-22 15:08:39,463 - INFO - URL文件类型检测结果: pdf, type_value: 2
2025-09-22 15:08:39,463 - INFO - 准备启动线程处理上传任务，参数: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=AWS_Certified_Solutions_Architect_Professional.pdf, type=2
2025-09-22 15:08:39,464 - INFO - 开始执行上传任务: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=AWS_Certified_Solutions_Architect_Professional.pdf, value=, type=2
2025-09-22 15:08:39,464 - INFO - 127.0.0.1 - - [22/Sep/2025 15:08:39] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:08:39,464 - INFO - 处理PDF文件
2025-09-22 15:08:39,465 - INFO - 开始处理PDF文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:08:39,495 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90
2025-09-22 15:08:39,499 - INFO - 下载网络文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:08:39,745 - INFO - 文件下载完成: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:08:39,746 - INFO - 开始将PDF转换为图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:08:40,134 - INFO - 使用PyMuPDF进行PDF转换
2025-09-22 15:08:40,267 - INFO - 保存第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\page_001.png
2025-09-22 15:08:40,340 - INFO - 保存第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\page_002.png
2025-09-22 15:08:40,340 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:08:40,341 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:08:40,341 - INFO - 处理第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\page_001.png
2025-09-22 15:08:40,342 - INFO - 处理第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90\page_002.png
2025-09-22 15:08:40,342 - INFO - PDF文件 AWS_Certified_Solutions_Architect_Professional.pdf 处理完成
2025-09-22 15:08:40,353 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_q9re9q90
2025-09-22 15:08:40,354 - INFO - PDF处理结果: {'success': True, 'total_pages': 2, 'processed_pages': [{'page_number': 1, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_q9re9q90\\page_001.png', 'image_size': 163598, 'status': 'processed'}, {'page_number': 2, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_q9re9q90\\page_002.png', 'image_size': 204631, 'status': 'processed'}]}
2025-09-22 15:08:40,354 - INFO - 文件处理任务完成
2025-09-22 15:09:27,415 - INFO - 开始上传文件或文字
2025-09-22 15:09:27,417 - INFO - 接收到参数: {'inputValue': '请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf'}
2025-09-22 15:09:27,418 - INFO - 检测输入中的文件链接: 请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,423 - INFO - 检测结果: 包含文件=True, 文件列表=['http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf', 'p://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf']
2025-09-22 15:09:27,425 - INFO - 检测文件类型: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,426 - INFO - 文件扩展名: .pdf
2025-09-22 15:09:27,427 - INFO - 输入文本中检测到文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, 类型: pdf
2025-09-22 15:09:27,428 - INFO - 准备启动线程处理上传任务，参数: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, type=2
2025-09-22 15:09:27,430 - INFO - 开始执行上传任务: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, value=请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, type=2
2025-09-22 15:09:27,431 - INFO - 127.0.0.1 - - [22/Sep/2025 15:09:27] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:09:27,432 - INFO - 处理PDF文件
2025-09-22 15:09:27,433 - INFO - 开始处理PDF文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,440 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips
2025-09-22 15:09:27,440 - INFO - 下载网络文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,544 - INFO - 文件下载完成: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,544 - INFO - 开始将PDF转换为图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:27,544 - INFO - 使用PyMuPDF进行PDF转换
2025-09-22 15:09:27,621 - INFO - 保存第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\page_001.png
2025-09-22 15:09:27,693 - INFO - 保存第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\page_002.png
2025-09-22 15:09:27,693 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:09:27,694 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:09:27,694 - INFO - 处理第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\page_001.png
2025-09-22 15:09:27,695 - INFO - 处理第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips\page_002.png
2025-09-22 15:09:27,695 - INFO - PDF文件 1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf 处理完成
2025-09-22 15:09:27,706 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_vgs9vips
2025-09-22 15:09:27,706 - INFO - PDF处理结果: {'success': True, 'total_pages': 2, 'processed_pages': [{'page_number': 1, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_vgs9vips\\page_001.png', 'image_size': 163598, 'status': 'processed'}, {'page_number': 2, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_vgs9vips\\page_002.png', 'image_size': 204631, 'status': 'processed'}]}
2025-09-22 15:09:27,706 - INFO - 文件处理任务完成
2025-09-22 15:09:29,490 - INFO - 开始上传文件或文字
2025-09-22 15:09:29,490 - INFO - 接收到参数: {'inputValue': '请分析这张图片：https://example.com/image.jpg'}
2025-09-22 15:09:29,491 - INFO - 检测输入中的文件链接: 请分析这张图片：https://example.com/image.jpg
2025-09-22 15:09:29,491 - INFO - 检测结果: 包含文件=True, 文件列表=['https://example.com/image.jpg', 's://example.com/image.jpg']
2025-09-22 15:09:29,491 - INFO - 检测文件类型: https://example.com/image.jpg
2025-09-22 15:09:29,491 - INFO - 文件扩展名: .jpg
2025-09-22 15:09:29,491 - INFO - 输入文本中检测到文件: https://example.com/image.jpg, 类型: image
2025-09-22 15:09:29,492 - INFO - 准备启动线程处理上传任务，参数: url=, name=, type=1
2025-09-22 15:09:29,492 - INFO - 开始执行上传任务: url=, name=, value=请分析这张图片：https://example.com/image.jpg, type=1
2025-09-22 15:09:29,492 - INFO - 127.0.0.1 - - [22/Sep/2025 15:09:29] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:09:29,492 - INFO - 其他类型文件处理: type=1
2025-09-22 15:09:29,493 - INFO - 文件处理任务完成
2025-09-22 15:09:31,553 - INFO - 开始上传文件或文字
2025-09-22 15:09:31,554 - INFO - 接收到参数: {'inputValue': '这是一段纯文本，不包含任何文件链接。'}
2025-09-22 15:09:31,554 - INFO - 检测输入中的文件链接: 这是一段纯文本，不包含任何文件链接。
2025-09-22 15:09:31,554 - INFO - 检测结果: 包含文件=False, 文件列表=[]
2025-09-22 15:09:31,555 - INFO - 准备启动线程处理上传任务，参数: url=, name=, type=1
2025-09-22 15:09:31,555 - INFO - 开始执行上传任务: url=, name=, value=这是一段纯文本，不包含任何文件链接。, type=1
2025-09-22 15:09:31,555 - INFO - 127.0.0.1 - - [22/Sep/2025 15:09:31] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:09:31,555 - INFO - 其他类型文件处理: type=1
2025-09-22 15:09:31,556 - INFO - 文件处理任务完成
2025-09-22 15:09:33,619 - INFO - 开始上传文件或文字
2025-09-22 15:09:33,621 - INFO - 接收到参数: {'type': 2, 'url': 'http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf', 'imageName': 'test.pdf'}
2025-09-22 15:09:33,621 - INFO - 检测文件类型: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:33,621 - INFO - 文件扩展名: .pdf
2025-09-22 15:09:33,621 - INFO - URL文件类型检测结果: pdf, type_value: 2
2025-09-22 15:09:33,622 - INFO - 准备启动线程处理上传任务，参数: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=test.pdf, type=2
2025-09-22 15:09:33,623 - INFO - 开始执行上传任务: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=test.pdf, value=, type=2
2025-09-22 15:09:33,623 - INFO - 127.0.0.1 - - [22/Sep/2025 15:09:33] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:09:33,624 - INFO - 处理PDF文件
2025-09-22 15:09:33,625 - INFO - 开始处理PDF文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:33,628 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75
2025-09-22 15:09:33,629 - INFO - 下载网络文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:33,736 - INFO - 文件下载完成: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:33,737 - INFO - 开始将PDF转换为图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:09:33,737 - INFO - 使用PyMuPDF进行PDF转换
2025-09-22 15:09:33,811 - INFO - 保存第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\page_001.png
2025-09-22 15:09:33,882 - INFO - 保存第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\page_002.png
2025-09-22 15:09:33,883 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:09:33,883 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:09:33,884 - INFO - 处理第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\page_001.png
2025-09-22 15:09:33,884 - INFO - 处理第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75\page_002.png
2025-09-22 15:09:33,884 - INFO - PDF文件 test.pdf 处理完成
2025-09-22 15:09:33,895 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_jrn8md75
2025-09-22 15:09:33,895 - INFO - PDF处理结果: {'success': True, 'total_pages': 2, 'processed_pages': [{'page_number': 1, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_jrn8md75\\page_001.png', 'image_size': 163598, 'status': 'processed'}, {'page_number': 2, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_jrn8md75\\page_002.png', 'image_size': 204631, 'status': 'processed'}]}
2025-09-22 15:09:33,896 - INFO - 文件处理任务完成
2025-09-22 15:09:56,636 - INFO -  * Detected change in 'E:\\workspace\\cocos\\mm-rag\\pdf_processor_test.py', reloading
2025-09-22 15:09:56,723 - INFO -  * Restarting with stat
2025-09-22 15:09:57,846 - INFO - PDF处理测试服务启动中...
2025-09-22 15:09:57,846 - INFO - 测试服务启动完成，监听端口 18052
2025-09-22 15:09:57,885 - WARNING -  * Debugger is active!
2025-09-22 15:09:57,891 - INFO -  * Debugger PIN: 503-295-468
2025-09-22 15:10:17,091 - INFO - PDF处理测试服务启动中...
2025-09-22 15:10:17,091 - INFO - 测试服务启动完成，监听端口 18052
2025-09-22 15:10:17,144 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:18052
 * Running on http://************:18052
2025-09-22 15:10:17,145 - INFO - [33mPress CTRL+C to quit[0m
2025-09-22 15:10:17,147 - INFO -  * Restarting with stat
2025-09-22 15:10:18,197 - INFO - PDF处理测试服务启动中...
2025-09-22 15:10:18,197 - INFO - 测试服务启动完成，监听端口 18052
2025-09-22 15:10:18,231 - WARNING -  * Debugger is active!
2025-09-22 15:10:18,240 - INFO -  * Debugger PIN: 503-295-468
2025-09-22 15:10:39,298 - INFO - 开始上传文件或文字
2025-09-22 15:10:39,299 - INFO - 接收到参数: {'inputValue': '请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf'}
2025-09-22 15:10:39,299 - INFO - 检测输入中的文件链接: 请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,300 - INFO - 检测结果: 包含文件=True, 文件列表=['http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf', 'p://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf']
2025-09-22 15:10:39,300 - INFO - 检测文件类型: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,301 - INFO - 文件扩展名: .pdf
2025-09-22 15:10:39,301 - INFO - 输入文本中检测到文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, 类型: pdf
2025-09-22 15:10:39,301 - INFO - 准备启动线程处理上传任务，参数: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, type=2
2025-09-22 15:10:39,302 - INFO - 开始执行上传任务: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, value=请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, type=2
2025-09-22 15:10:39,302 - INFO - 127.0.0.1 - - [22/Sep/2025 15:10:39] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:10:39,302 - INFO - 处理PDF文件
2025-09-22 15:10:39,303 - INFO - 开始处理PDF文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,310 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0
2025-09-22 15:10:39,310 - INFO - 下载网络文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,524 - INFO - 文件下载完成: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,524 - INFO - 开始将PDF转换为图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:39,639 - INFO - 使用PyMuPDF进行PDF转换
2025-09-22 15:10:39,713 - INFO - 保存第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\page_001.png
2025-09-22 15:10:39,786 - INFO - 保存第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\page_002.png
2025-09-22 15:10:39,786 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:10:39,787 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:10:39,787 - INFO - 处理第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\page_001.png
2025-09-22 15:10:39,787 - INFO - 处理第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0\page_002.png
2025-09-22 15:10:39,788 - INFO - PDF文件 1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf 处理完成
2025-09-22 15:10:39,799 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_w_zoza_0
2025-09-22 15:10:39,800 - INFO - PDF处理结果: {'success': True, 'total_pages': 2, 'processed_pages': [{'page_number': 1, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_w_zoza_0\\page_001.png', 'image_size': 163598, 'status': 'processed'}, {'page_number': 2, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_w_zoza_0\\page_002.png', 'image_size': 204631, 'status': 'processed'}]}
2025-09-22 15:10:39,800 - INFO - 文件处理任务完成
2025-09-22 15:10:41,364 - INFO - 开始上传文件或文字
2025-09-22 15:10:41,364 - INFO - 接收到参数: {'inputValue': '请分析这张图片：https://example.com/image.jpg'}
2025-09-22 15:10:41,364 - INFO - 检测输入中的文件链接: 请分析这张图片：https://example.com/image.jpg
2025-09-22 15:10:41,365 - INFO - 检测结果: 包含文件=True, 文件列表=['https://example.com/image.jpg', 's://example.com/image.jpg']
2025-09-22 15:10:41,365 - INFO - 检测文件类型: https://example.com/image.jpg
2025-09-22 15:10:41,365 - INFO - 文件扩展名: .jpg
2025-09-22 15:10:41,365 - INFO - 输入文本中检测到文件: https://example.com/image.jpg, 类型: image
2025-09-22 15:10:41,365 - INFO - 准备启动线程处理上传任务，参数: url=https://example.com/image.jpg, name=image.jpg, type=0
2025-09-22 15:10:41,366 - INFO - 开始执行上传任务: url=https://example.com/image.jpg, name=image.jpg, value=请分析这张图片：https://example.com/image.jpg, type=0
2025-09-22 15:10:41,366 - INFO - 127.0.0.1 - - [22/Sep/2025 15:10:41] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:10:41,366 - INFO - 其他类型文件处理: type=0
2025-09-22 15:10:41,367 - INFO - 文件处理任务完成
2025-09-22 15:10:43,407 - INFO - 开始上传文件或文字
2025-09-22 15:10:43,407 - INFO - 接收到参数: {'inputValue': '这是一段纯文本，不包含任何文件链接。'}
2025-09-22 15:10:43,407 - INFO - 检测输入中的文件链接: 这是一段纯文本，不包含任何文件链接。
2025-09-22 15:10:43,407 - INFO - 检测结果: 包含文件=False, 文件列表=[]
2025-09-22 15:10:43,408 - INFO - 准备启动线程处理上传任务，参数: url=, name=, type=1
2025-09-22 15:10:43,408 - INFO - 开始执行上传任务: url=, name=, value=这是一段纯文本，不包含任何文件链接。, type=1
2025-09-22 15:10:43,408 - INFO - 127.0.0.1 - - [22/Sep/2025 15:10:43] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:10:43,409 - INFO - 其他类型文件处理: type=1
2025-09-22 15:10:43,409 - INFO - 文件处理任务完成
2025-09-22 15:10:45,451 - INFO - 开始上传文件或文字
2025-09-22 15:10:45,451 - INFO - 接收到参数: {'type': 2, 'url': 'http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf', 'imageName': 'test.pdf'}
2025-09-22 15:10:45,451 - INFO - 检测文件类型: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:45,451 - INFO - 文件扩展名: .pdf
2025-09-22 15:10:45,452 - INFO - URL文件类型检测结果: pdf, type_value: 2
2025-09-22 15:10:45,452 - INFO - 准备启动线程处理上传任务，参数: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=test.pdf, type=2
2025-09-22 15:10:45,452 - INFO - 开始执行上传任务: url=http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf, name=test.pdf, value=, type=2
2025-09-22 15:10:45,452 - INFO - 127.0.0.1 - - [22/Sep/2025 15:10:45] "POST /upload HTTP/1.1" 200 -
2025-09-22 15:10:45,453 - INFO - 处理PDF文件
2025-09-22 15:10:45,453 - INFO - 开始处理PDF文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:45,455 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025
2025-09-22 15:10:45,455 - INFO - 下载网络文件: http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:45,599 - INFO - 文件下载完成: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:45,601 - INFO - 开始将PDF转换为图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf
2025-09-22 15:10:45,602 - INFO - 使用PyMuPDF进行PDF转换
2025-09-22 15:10:45,679 - INFO - 保存第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\page_001.png
2025-09-22 15:10:45,751 - INFO - 保存第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\page_002.png
2025-09-22 15:10:45,752 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:10:45,752 - INFO - PDF转换完成，共生成 2 张图片
2025-09-22 15:10:45,753 - INFO - 处理第 1 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\page_001.png
2025-09-22 15:10:45,753 - INFO - 处理第 2 页图片: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025\page_002.png
2025-09-22 15:10:45,753 - INFO - PDF文件 test.pdf 处理完成
2025-09-22 15:10:45,764 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\pdf_processing_8rmgw025
2025-09-22 15:10:45,765 - INFO - PDF处理结果: {'success': True, 'total_pages': 2, 'processed_pages': [{'page_number': 1, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_8rmgw025\\page_001.png', 'image_size': 163598, 'status': 'processed'}, {'page_number': 2, 'image_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\pdf_processing_8rmgw025\\page_002.png', 'image_size': 204631, 'status': 'processed'}]}
2025-09-22 15:10:45,765 - INFO - 文件处理任务完成
