#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyMuPDF是否能正常工作
"""

import tempfile
import os
import requests

def test_pymupdf():
    """测试PyMuPDF功能"""
    try:
        import fitz  # PyMuPDF
        print("✓ PyMuPDF导入成功")
        
        # 测试PDF文件URL
        pdf_url = "http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf"
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='test_pdf_')
        print(f"创建临时目录: {temp_dir}")
        
        # 下载PDF文件
        print("开始下载PDF文件...")
        response = requests.get(pdf_url, timeout=30)
        response.raise_for_status()
        
        pdf_path = os.path.join(temp_dir, "test.pdf")
        with open(pdf_path, 'wb') as f:
            f.write(response.content)
        print(f"PDF文件下载完成: {pdf_path}")
        
        # 使用PyMuPDF打开PDF
        print("使用PyMuPDF打开PDF...")
        pdf_document = fitz.open(pdf_path)
        page_count = len(pdf_document)
        print(f"✓ PDF打开成功，共 {page_count} 页")
        
        # 转换第一页为图片
        if page_count > 0:
            print("转换第一页为图片...")
            page = pdf_document.load_page(0)
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)
            
            image_path = os.path.join(temp_dir, "page_001.png")
            pix.save(image_path)
            print(f"✓ 第一页转换成功: {image_path}")
            
            # 检查图片文件大小
            image_size = os.path.getsize(image_path)
            print(f"图片文件大小: {image_size} 字节")
        
        pdf_document.close()
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir)
        print("✓ 临时文件清理完成")
        
        print("🎉 PyMuPDF测试成功！")
        return True
        
    except ImportError as e:
        print(f"✗ PyMuPDF导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ PyMuPDF测试失败: {e}")
        return False

if __name__ == "__main__":
    test_pymupdf()
