#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OCR功能
"""

def test_paddleocr():
    """测试PaddleOCR是否正常工作"""
    try:
        print("测试PaddleOCR导入...")
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        
        print("初始化PaddleOCR...")
        # 使用简单配置，避免下载过多模型
        ocr = PaddleOCR(use_angle_cls=False, lang='en', show_log=False)
        print("✅ PaddleOCR初始化成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        return False

def test_base64_encoding():
    """测试base64编码功能"""
    try:
        import base64
        import tempfile
        import os
        from PIL import Image
        
        print("测试base64编码...")
        
        # 创建一个简单的测试图片
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            # 创建一个简单的白色图片
            img = Image.new('RGB', (100, 50), color='white')
            img.save(tmp_file.name)
            
            # 测试base64编码
            with open(tmp_file.name, 'rb') as image_file:
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')
            
            print(f"✅ base64编码成功，长度: {len(base64_image)}")
            
            # 清理临时文件
            os.unlink(tmp_file.name)
            
        return True
        
    except Exception as e:
        print(f"❌ base64编码测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("OCR功能测试")
    print("=" * 50)
    
    # 测试base64编码
    base64_ok = test_base64_encoding()
    
    print()
    
    # 测试PaddleOCR
    ocr_ok = test_paddleocr()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"Base64编码: {'✅ 正常' if base64_ok else '❌ 失败'}")
    print(f"PaddleOCR: {'✅ 正常' if ocr_ok else '❌ 失败'}")
    
    if base64_ok and ocr_ok:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
    print("=" * 50)
