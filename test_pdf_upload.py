#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF上传功能
"""

import requests
import json
import time

def test_pdf_upload():
    """测试PDF上传功能"""
    print("开始测试PDF上传功能...")
    
    # 测试URL
    url = "http://localhost:18052/upload"
    
    # 测试数据
    data = {
        "type": 2,
        "url": "http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf",
        "imageName": "AWS_Certified_Solutions_Architect_Professional.pdf"
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=data, timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✓ PDF上传请求成功")
                print(f"返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return True
            except json.JSONDecodeError:
                print("✓ PDF上传请求成功（返回非JSON格式）")
                return True
        else:
            print("✗ PDF上传请求失败")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务已启动")
        return False
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def test_health_check():
    """测试服务健康状态"""
    print("检查服务健康状态...")
    
    try:
        response = requests.get("http://localhost:18052", timeout=10)
        if response.status_code == 200:
            print("✓ 服务运行正常")
            return True
        else:
            print(f"✗ 服务状态异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ 服务未启动或无法连接")
        return False
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("PDF处理功能测试")
    print("=" * 50)
    
    # 健康检查
    if test_health_check():
        print()
        # PDF上传测试
        test_pdf_upload()
    else:
        print("请先启动服务: python newDashVector.py")
