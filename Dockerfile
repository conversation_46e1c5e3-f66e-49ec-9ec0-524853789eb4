# PDF文档处理和向量化存储系统 Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # PDF处理依赖
    poppler-utils \
    # 图像处理依赖
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # 网络工具
    wget \
    curl \
    # 编译工具
    gcc \
    g++ \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建必要的目录
RUN mkdir -p /app/logs \
    && mkdir -p /app/temp \
    && mkdir -p /app/models

# 复制应用代码
COPY newDashVector.py .

# 设置权限
RUN chmod +x newDashVector.py

# 预下载PaddleOCR模型（可选，减少首次运行时间）
RUN python -c "
try:
    from paddleocr import PaddleOCR
    ocr = PaddleOCR(use_angle_cls=True, lang='ch')
    print('PaddleOCR模型预下载完成')
except Exception as e:
    print(f'PaddleOCR模型预下载失败: {e}')
"

# 暴露端口
EXPOSE 18052

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:18052/ || exit 1

# 启动命令
CMD ["python", "newDashVector.py"]
