# newDashVector.py
import json
from flask import Flask, request
import dashscope
from dashvector import Client, Doc
import uuid
from threading import Thread
import pymysql
from dbutils.pooled_db import PooledDB
from datetime import datetime
from http import HTTPStatus
from openai import OpenAI
# 添加日志模块
import logging
# 添加PDF处理相关模块
import os
import tempfile
import shutil
from urllib.parse import urlparse
from pathlib import Path
import re
import requests
from io import BytesIO

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 阿里dashscope相关配置
dashscope_api_key = 'sk-6f882d82aa9645c1a799e95c165a86c6'
dashvector_api_key = 'sk-UeyJDmZHk6HD5rPfRQv6PZ6301q0Q1C1C091F8A9A11EEBF5562E118F409F5'
dashvector_endpoint = 'vrs-cn-0mm44scjt000bq.dashvector.cn-beijing.aliyuncs.com'

# 创建数据库连接池
def create_conn_pool():
    logger.info("创建数据库连接池")
    pool = PooledDB(
        creator=pymysql,  # 使用pymysql连接数据库
        maxconnections=10,  # 连接池允许的最大连接数
        mincached=2,  # 初始化时，连接池中至少创建的空闲的连接数
        maxcached=5,  # 连接池中最多闲置的连接数
        maxshared=3,  # 连接池中最多共享的连接数
        blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
        maxusage=None,  # 一个连接最多被重复使用的次数
        setsession=[],  # 开始会话前执行的命令列表
        ping=0,  # ping MySQL服务端，检查服务是否可用
        #host='innerdev001.mysql.rds.aliyuncs.com',
        host='hzshkjdev001.mysql.rds.aliyuncs.com',
        port=3306,
        user='root',
        password='peilian123a!',
        database='cool-admin',
        charset='utf8mb4'
    )
    logger.info("数据库连接池创建成功")
    return pool

# 文件类型检测函数
def detect_file_type(file_path_or_url):
    """
    检测文件类型，支持URL和本地路径
    返回: 'pdf', 'image', 'text', 'unknown'
    """
    try:
        logger.info(f"检测文件类型: {file_path_or_url}")

        # 提取文件扩展名
        if file_path_or_url.startswith(('http://', 'https://')):
            # URL路径
            parsed_url = urlparse(file_path_or_url)
            file_path = parsed_url.path
        else:
            # 本地路径
            file_path = file_path_or_url

        # 获取文件扩展名
        file_extension = Path(file_path).suffix.lower()
        logger.info(f"文件扩展名: {file_extension}")

        # 根据扩展名判断文件类型
        if file_extension == '.pdf':
            return 'pdf'
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            return 'image'
        elif file_extension in ['.txt', '.md', '.doc', '.docx']:
            return 'text'
        else:
            # 尝试通过URL模式匹配
            if re.search(r'\.pdf(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'pdf'
            elif re.search(r'\.(jpg|jpeg|png|bmp|gif|tiff|webp)(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'image'
            else:
                return 'unknown'

    except Exception as e:
        logger.error(f"文件类型检测失败: {e}")
        return 'unknown'

# 检测输入中是否包含文件链接或路径
def detect_file_in_input(input_text):
    """
    检测输入文本中是否包含文件链接或路径
    返回: (是否包含文件, 文件路径列表)
    """
    try:
        if not input_text:
            return False, []

        logger.info(f"检测输入中的文件链接: {input_text}")

        # URL模式匹配
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+\.(pdf|jpg|jpeg|png|bmp|gif|tiff|webp|txt|md|doc|docx)'
        urls = re.findall(url_pattern, input_text, re.IGNORECASE)

        # 本地路径模式匹配
        path_pattern = r'[A-Za-z]:[\\\/][^\s<>"{}|\\^`\[\]]*\.(pdf|jpg|jpeg|png|bmp|gif|tiff|webp|txt|md|doc|docx)'
        paths = re.findall(path_pattern, input_text, re.IGNORECASE)

        # 提取完整的文件路径
        file_paths = []
        for match in re.finditer(url_pattern, input_text, re.IGNORECASE):
            file_paths.append(match.group(0))
        for match in re.finditer(path_pattern, input_text, re.IGNORECASE):
            file_paths.append(match.group(0))

        has_files = len(file_paths) > 0
        logger.info(f"检测结果: 包含文件={has_files}, 文件列表={file_paths}")

        return has_files, file_paths

    except Exception as e:
        logger.error(f"文件链接检测失败: {e}")
        return False, []

# 获取图片路径，将图片转为向量，并向量存储到向量数据库中
@app.route("/upload", methods=["POST"])
def upload():
    logger.info("开始上传文件或文字")

    # 解析传参
    param = request.json
    logger.info(f"接收到参数: {param}")
    param_url = ''
    param_image_name = ''
    param_input_value = ''
    type_value = param.get('type', 1)  # 默认为文本类型

    if 'url' in param:
        param_url = param['url']
    if 'imageName' in param:
        param_image_name = param['imageName']
    if 'inputValue' in param:
        param_input_value = param['inputValue']

    # 智能检测文件类型
    detected_files = []
    if param_url:
        # 检测URL中的文件类型
        file_type = detect_file_type(param_url)
        if file_type == 'pdf':
            type_value = 2  # PDF文件类型
        elif file_type == 'image':
            type_value = 0  # 图片文件类型
        logger.info(f"URL文件类型检测结果: {file_type}, type_value: {type_value}")

    if param_input_value:
        # 检测输入文本中是否包含文件链接
        has_files, file_paths = detect_file_in_input(param_input_value)
        if has_files:
            detected_files = file_paths
            # 如果检测到文件，优先处理第一个文件
            first_file = file_paths[0]
            file_type = detect_file_type(first_file)
            if file_type == 'pdf':
                type_value = 2
                param_url = first_file  # 将检测到的文件路径作为URL
                if not param_image_name:
                    param_image_name = Path(first_file).name
            elif file_type == 'image':
                type_value = 0
                param_url = first_file
                if not param_image_name:
                    param_image_name = Path(first_file).name
            logger.info(f"输入文本中检测到文件: {first_file}, 类型: {file_type}")

    logger.info(f"准备启动线程处理上传任务，参数: url={param_url}, name={param_image_name}, value={param_input_value}, type={type_value}")
    t = Thread(target=do_upload, args=(param_url, param_image_name, param_input_value, type_value, detected_files))
    t.start()

    return {'status': 'ok', 'message': '文件处理任务已启动', 'detected_files': detected_files}

# 获取图片路径，将图片转为向量，并向量存储到向量数据库中
def do_upload(param_url, param_image_name, param_input_value, type_value, detected_files=None):
    logger.info(f"开始执行上传任务: url={param_url}, name={param_image_name}, value={param_input_value}, type={type_value}")
    random_uuid = uuid.uuid4()

    if type_value == 0:  # 图片文件
        # 查找相关数据
        count_number = count_file(param_url)
        if count_number > 0:
            logger.info("文件已存在，跳过处理")
            return
        # 将相关数据存储到数据库
        insert_file_v2(param_url, param_image_name, random_uuid, 0)
        # 将内容存储到向量数据库
        update_embeddings(param_url, param_image_name, param_input_value, type_value, random_uuid)

    elif type_value == 1:  # 文本内容
        # 将相关数据存储到数据库
        insert_file(param_input_value, random_uuid, 0)
        # 将内容存储到向量数据库
        update_embeddings(param_url, param_image_name, param_input_value, type_value, random_uuid)

    elif type_value == 2:  # PDF文件
        # 查找相关数据
        count_number = count_file(param_url)
        if count_number > 0:
            logger.info("PDF文件已存在，跳过处理")
            return
        # 将PDF文件信息存储到数据库
        insert_file_v2(param_url, param_image_name, random_uuid, 0)
        # 处理PDF文件
        process_pdf_file(param_url, param_image_name, random_uuid)

    logger.info("文件处理任务完成")

# PDF文件处理函数
def process_pdf_file(pdf_url, pdf_name, file_uuid):
    """
    处理PDF文件：转换为图片，进行图像理解和OCR识别，然后向量化存储
    """
    logger.info(f"开始处理PDF文件: {pdf_url}")
    temp_dir = None

    # 更新处理状态为处理中
    update_pdf_processing_status(file_uuid, 1, "开始处理PDF文件")

    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='pdf_processing_')
        logger.info(f"创建临时目录: {temp_dir}")

        # 下载或读取PDF文件
        pdf_path = download_or_copy_file(pdf_url, temp_dir)
        if not pdf_path:
            logger.error("PDF文件下载或复制失败")
            return

        # 将PDF转换为图片
        image_paths = convert_pdf_to_images(pdf_path, temp_dir)
        if not image_paths:
            logger.error("PDF转图片失败")
            return

        logger.info(f"PDF转换完成，共生成 {len(image_paths)} 张图片")

        # 处理每一页图片
        for page_num, image_path in enumerate(image_paths, 1):
            logger.info(f"处理第 {page_num} 页图片: {image_path}")

            # 生成页面唯一ID
            page_uuid = f"{file_uuid}_page_{page_num}"

            # 图像理解处理
            try:
                image_understanding_result = process_image_understanding(image_path)
                if image_understanding_result:
                    # 将图像理解结果向量化存储
                    store_image_understanding_vector(
                        image_path, pdf_name, image_understanding_result,
                        page_uuid, page_num, pdf_url
                    )
                    logger.info(f"第 {page_num} 页图像理解处理完成")
            except Exception as e:
                logger.error(f"第 {page_num} 页图像理解处理失败: {e}")

            # OCR文字识别处理
            try:
                ocr_text = process_ocr_recognition(image_path)
                if ocr_text and ocr_text.strip():
                    # 将OCR文本向量化存储
                    store_ocr_text_vector(
                        ocr_text, pdf_name, page_uuid, page_num, pdf_url
                    )
                    logger.info(f"第 {page_num} 页OCR处理完成")
            except Exception as e:
                logger.error(f"第 {page_num} 页OCR处理失败: {e}")

        logger.info(f"PDF文件 {pdf_name} 处理完成")
        # 更新处理状态为完成
        update_pdf_processing_status(file_uuid, 2, f"PDF处理完成，共处理{len(image_paths)}页")

    except Exception as e:
        logger.error(f"PDF文件处理过程中发生错误: {e}")
        # 更新处理状态为失败
        update_pdf_processing_status(file_uuid, 3, f"PDF处理失败: {str(e)}")
    finally:
        # 清理临时文件
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"清理临时目录: {temp_dir}")
            except Exception as e:
                logger.error(f"清理临时目录失败: {e}")

# 下载或复制文件到临时目录
def download_or_copy_file(file_url, temp_dir):
    """
    下载URL文件或复制本地文件到临时目录
    """
    try:
        if file_url.startswith(('http://', 'https://')):
            # 下载网络文件
            logger.info(f"下载网络文件: {file_url}")
            response = requests.get(file_url, timeout=30)
            response.raise_for_status()

            # 生成临时文件名
            file_name = Path(urlparse(file_url).path).name
            if not file_name or '.' not in file_name:
                file_name = 'downloaded_file.pdf'

            temp_file_path = os.path.join(temp_dir, file_name)
            with open(temp_file_path, 'wb') as f:
                f.write(response.content)

            logger.info(f"文件下载完成: {temp_file_path}")
            return temp_file_path
        else:
            # 复制本地文件
            logger.info(f"复制本地文件: {file_url}")
            if os.path.exists(file_url):
                file_name = Path(file_url).name
                temp_file_path = os.path.join(temp_dir, file_name)
                shutil.copy2(file_url, temp_file_path)
                logger.info(f"文件复制完成: {temp_file_path}")
                return temp_file_path
            else:
                logger.error(f"本地文件不存在: {file_url}")
                return None

    except Exception as e:
        logger.error(f"文件下载或复制失败: {e}")
        return None

# PDF转图片函数
def convert_pdf_to_images(pdf_path, output_dir):
    """
    将PDF文件转换为图片
    使用pdf2image库，如果未安装则尝试使用其他方法
    """
    try:
        logger.info(f"开始将PDF转换为图片: {pdf_path}")

        # 尝试导入pdf2image
        try:
            from pdf2image import convert_from_path
            logger.info("使用pdf2image库进行PDF转换")

            # 转换PDF为图片
            images = convert_from_path(
                pdf_path,
                dpi=200,  # 设置DPI，影响图片质量
                fmt='PNG'  # 输出格式
            )

            image_paths = []
            for i, image in enumerate(images, 1):
                image_filename = f"page_{i:03d}.png"
                image_path = os.path.join(output_dir, image_filename)
                image.save(image_path, 'PNG')
                image_paths.append(image_path)
                logger.info(f"保存第 {i} 页图片: {image_path}")

            logger.info(f"PDF转换完成，共生成 {len(image_paths)} 张图片")
            return image_paths

        except ImportError:
            logger.warning("pdf2image库未安装，尝试使用替代方案")
            return convert_pdf_to_images_fallback(pdf_path, output_dir)
        except Exception as e:
            logger.warning(f"pdf2image转换失败: {e}，尝试使用替代方案")
            return convert_pdf_to_images_fallback(pdf_path, output_dir)

    except Exception as e:
        logger.error(f"PDF转图片失败: {e}")
        return []

# PDF转图片备用方案
def convert_pdf_to_images_fallback(pdf_path, output_dir):
    """
    PDF转图片的备用方案，使用PyMuPDF (fitz)
    """
    try:
        logger.info("尝试使用PyMuPDF进行PDF转换")
        import fitz  # PyMuPDF

        # 打开PDF文件
        pdf_document = fitz.open(pdf_path)
        image_paths = []

        for page_num in range(len(pdf_document)):
            # 获取页面
            page = pdf_document.load_page(page_num)

            # 设置缩放比例（影响图片质量）
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)

            # 保存图片
            image_filename = f"page_{page_num + 1:03d}.png"
            image_path = os.path.join(output_dir, image_filename)
            pix.save(image_path)
            image_paths.append(image_path)
            logger.info(f"保存第 {page_num + 1} 页图片: {image_path}")

        pdf_document.close()
        logger.info(f"PDF转换完成，共生成 {len(image_paths)} 张图片")
        return image_paths

    except ImportError:
        logger.error("PyMuPDF库也未安装，无法进行PDF转换")
        return []
    except Exception as e:
        logger.error(f"PyMuPDF转换失败: {e}")
        return []

# 图像理解处理函数
def process_image_understanding(image_path):
    """
    使用阿里云通义千问VL模型对图片进行语义理解
    """
    try:
        logger.info(f"开始图像理解处理: {image_path}")

        # 将本地图片转换为可访问的URL或base64
        # 这里我们使用现有的llm_go函数，它支持图片URL

        # 如果是本地文件，需要先上传到可访问的位置或转换为base64
        # 为了简化，这里直接使用文件路径（需要确保模型能访问）

        # 构建图像理解的提示词
        prompt = """请详细描述这张图片的内容，包括：
1. 图片中的主要对象和元素
2. 文字内容（如果有）
3. 图片的整体布局和结构
4. 图片可能表达的含义或主题
5. 任何重要的细节信息

请用中文回答，描述要详细且准确。"""

        # 调用图像理解模型
        result = llm_go(prompt, image_path)

        if result and 'answer' in result:
            logger.info(f"图像理解完成，结果长度: {len(result['answer'])}")
            return result['answer']
        else:
            logger.error("图像理解模型返回空结果")
            return None

    except Exception as e:
        logger.error(f"图像理解处理失败: {e}")
        return None

# 存储图像理解结果的向量
def store_image_understanding_vector(image_path, pdf_name, understanding_result, page_uuid, page_num, pdf_url):
    """
    将图像理解结果转换为向量并存储到向量数据库
    """
    try:
        logger.info(f"存储图像理解向量: {page_uuid}")

        # 初始化 dashvector client
        client = Client(
            api_key=dashvector_api_key,
            endpoint=dashvector_endpoint
        )

        # 生成图像理解结果的向量
        vectors = generate_embeddings('', understanding_result, 1)  # 使用文本向量化
        if not vectors:
            logger.error("图像理解结果向量化失败")
            return

        # 连接图片向量集合
        collection = client.get('test')

        # 构建存储字段
        fields = {
            'title': f"{pdf_name}_page_{page_num}_understanding",
            'url': pdf_url,
            'value': understanding_result,
            'id': page_uuid,
            'memo1': '3',  # 新类型：PDF图像理解
            'memo2': str(page_num),  # 页码
            'memo3': 'image_understanding',  # 处理类型
            'memo4': pdf_name,  # 原始PDF文件名
            'memo5': image_path  # 图片路径（临时）
        }

        # 插入向量数据
        ret = collection.insert([
            Doc(
                id=f"{page_uuid}_understanding",
                vector=vectors,
                fields=fields
            )
        ])

        logger.info(f"图像理解向量存储成功: {ret}")

    except Exception as e:
        logger.error(f"图像理解向量存储失败: {e}")

# OCR文字识别处理函数
def process_ocr_recognition(image_path):
    """
    对图片进行OCR文字识别
    """
    try:
        logger.info(f"开始OCR文字识别: {image_path}")

        # 尝试使用PaddleOCR
        try:
            from paddleocr import PaddleOCR
            logger.info("使用PaddleOCR进行文字识别")

            # 初始化OCR模型（只需要中英文）
            ocr = PaddleOCR(use_angle_cls=True, lang='ch')

            # 进行OCR识别
            result = ocr.ocr(image_path, cls=True)

            # 提取文字内容
            text_content = []
            if result and result[0]:
                for line in result[0]:
                    if line and len(line) > 1:
                        text_content.append(line[1][0])  # 提取识别的文字

            ocr_text = '\n'.join(text_content)
            logger.info(f"OCR识别完成，提取文字长度: {len(ocr_text)}")
            return ocr_text

        except ImportError:
            logger.warning("PaddleOCR库未安装，尝试使用替代方案")
            return process_ocr_fallback(image_path)

    except Exception as e:
        logger.error(f"OCR文字识别失败: {e}")
        return ""

# OCR识别备用方案
def process_ocr_fallback(image_path):
    """
    OCR识别的备用方案，使用阿里云OCR服务或其他方案
    """
    try:
        logger.info("尝试使用备用OCR方案")

        # 这里可以集成阿里云OCR服务
        # 由于需要额外的API配置，暂时返回空字符串
        # 实际使用时可以配置阿里云OCR API

        logger.warning("备用OCR方案未实现，返回空字符串")
        return ""

    except Exception as e:
        logger.error(f"备用OCR方案失败: {e}")
        return ""

# 存储OCR文本向量
def store_ocr_text_vector(ocr_text, pdf_name, page_uuid, page_num, pdf_url):
    """
    将OCR识别的文本转换为向量并存储到向量数据库
    """
    try:
        logger.info(f"存储OCR文本向量: {page_uuid}")

        # 初始化 dashvector client
        client = Client(
            api_key=dashvector_api_key,
            endpoint=dashvector_endpoint
        )

        # 生成OCR文本的向量
        vectors = generate_embeddings('', ocr_text, 1)  # 使用文本向量化
        if not vectors:
            logger.error("OCR文本向量化失败")
            return

        # 连接文本向量集合
        collection = client.get('text_collection')

        # 构建存储字段
        fields = {
            'title': f"{pdf_name}_page_{page_num}_ocr",
            'url': pdf_url,
            'value': ocr_text,
            'id': page_uuid,
            'memo1': '4',  # 新类型：PDF OCR文本
            'memo2': str(page_num),  # 页码
            'memo3': 'ocr_text',  # 处理类型
            'memo4': pdf_name,  # 原始PDF文件名
            'memo5': ''  # 预留字段
        }

        # 插入向量数据
        ret = collection.insert([
            Doc(
                id=f"{page_uuid}_ocr",
                vector=vectors,
                fields=fields
            )
        ])

        logger.info(f"OCR文本向量存储成功: {ret}")

    except Exception as e:
        logger.error(f"OCR文本向量存储失败: {e}")

# 更新PDF文件处理状态
def update_pdf_processing_status(file_uuid, status, processing_info=None):
    """
    更新PDF文件的处理状态
    status: 0-待处理, 1-处理中, 2-处理完成, 3-处理失败
    """
    try:
        logger.info(f"更新PDF处理状态: {file_uuid}, status: {status}")

        # 构建更新SQL
        if processing_info:
            sql = "UPDATE tb_file_new SET do_number = %s, processing_info = %s WHERE random_uuid = %s"
            val = (status, processing_info, str(file_uuid))
        else:
            sql = "UPDATE tb_file_new SET do_number = %s WHERE random_uuid = %s"
            val = (status, str(file_uuid))

        result = insert_update_delete(sql, val)
        logger.info(f"PDF处理状态更新完成，影响行数: {result}")
        return result > 0

    except Exception as e:
        logger.error(f"更新PDF处理状态失败: {e}")
        return False

# 插入PDF页面处理记录
def insert_pdf_page_record(file_uuid, page_num, page_type, content, vector_id):
    """
    插入PDF页面处理记录
    page_type: 'image_understanding' 或 'ocr_text'
    """
    try:
        logger.info(f"插入PDF页面记录: {file_uuid}, page: {page_num}, type: {page_type}")

        sql = """INSERT INTO tb_pdf_pages
                 (file_uuid, page_num, page_type, content, vector_id, create_time)
                 VALUES (%s, %s, %s, %s, %s, %s)"""
        val = (str(file_uuid), page_num, page_type, content, vector_id, datetime.now())

        result = insert_update_delete(sql, val)
        logger.info(f"PDF页面记录插入完成，影响行数: {result}")
        return result > 0

    except Exception as e:
        logger.error(f"插入PDF页面记录失败: {e}")
        return False

# 查询数据是否存在
def count_file(param_url):
    try:
        logger.info(f"查询文件是否存在: {param_url}")
        sql = "SELECT count(1) FROM tb_file_new WHERE file_url = %s"
        result = select_data(sql, param_url)
        logger.info(f"查询结果: {result[0][0]}")
        return result[0][0]
    except Exception as e:
        logger.error(f"查询数据时发生错误: {e}")
        return 0

# 将数据插入到数据库
def insert_file(input_value, random_uuid, do_number):
    try:
        logger.info(f"插入文本数据到数据库: value={input_value}, uuid={random_uuid}")
        sql = "INSERT INTO tb_file_new (input_value,random_uuid,creat_time,status,do_number) VALUES (%s,%s,%s,%s,%s)"
        val = (input_value, random_uuid, datetime.now(), '1', do_number)
        insert_update_delete(sql, val)
        logger.info("文本数据插入成功")
    except Exception as e:
        logger.error(f"插入文本数据时发生错误: {e}")

# 将数据插入到数据库
def insert_file_v2(param_url, param_video_name, random_uuid, do_number):
    try:
        logger.info(f"插入文件数据到数据库: url={param_url}, name={param_video_name}, uuid={random_uuid}")
        sql = "INSERT INTO tb_file_new (file_url,file_name,random_uuid,creat_time,status,do_number) VALUES (%s,%s,%s,%s,%s,%s)"
        val = (param_url, param_video_name, random_uuid, datetime.now(), '1', do_number)
        insert_update_delete(sql, val)
        logger.info("文件数据插入成功")
    except Exception as e:
        logger.error(f"插入文件数据时发生错误: {e}")

# 新增/更新/删除数据
def insert_update_delete(sql, values):
    logger.info("执行数据库写入操作")
    if pool is None:
        logger.warning("数据库连接池未初始化，跳过数据库操作")
        return 1  # 返回模拟的影响行数

    try:
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, values)
                conn.commit()
                row_count = cursor.rowcount
                logger.info(f"数据库操作完成，影响行数: {row_count}")
                return row_count
    except Exception as e:
        logger.error(f"数据库操作失败: {e}")
        return 0

# 查询数据
def select_data(sql, values):
    logger.info("执行数据库查询操作")
    if pool is None:
        logger.warning("数据库连接池未初始化，返回空结果")
        return []

    try:
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, values)
                result = cursor.fetchall()
                logger.info(f"查询完成，返回 {len(result)} 条记录")
                return result
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        return []

# 插入问题记录到数据库
def insert_question_record(question_content, question_type, answer_content, 
                          question_token, answer_token, total_token, 
                          user_id, knowledge_id, question_url):
    """
    将查询记录插入到 question_record 表中，并返回主键ID
    """
    try:
        logger.info(f"插入问题记录: {question_content}")
        sql = """INSERT INTO question_record 
                 (questionContent, questionType, answerContent, questionToken, 
                  answerToken, totalToken, userId, knowledgeId, questionUrl) 
                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"""
        val = (question_content, question_type, answer_content, 
               question_token, answer_token, total_token, 
               user_id, knowledge_id, question_url)
        
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, val)
                conn.commit()
                record_id = cursor.lastrowid  # 获取插入记录的主键ID
                logger.info(f"问题记录插入成功，ID: {record_id}")
                return record_id
    except Exception as e:
        logger.error(f"插入问题记录时发生错误: {e}")
        return None

# 扣除用户token
def deduct_user_token(user_id, token_count):
    """
    根据用户ID扣除相应的token数量
    :param user_id: 用户ID
    :param token_count: 需要扣除的token数量
    :return: 扣除结果 True/False
    """
    try:
        logger.info(f"开始为用户 {user_id} 扣除 {token_count} 个token")
        
        # 查询用户当前token余额
        select_sql = "SELECT token FROM base_sys_user WHERE id = %s"
        result = select_data(select_sql, (user_id,))
        
        if not result:
            logger.warning(f"未找到用户 {user_id}")
            return False
            
        current_token = result[0][0] if result[0][0] else 0
        logger.info(f"用户 {user_id} 当前token余额: {current_token}")
        
        # 检查token余额是否足够
        #if current_token < token_count:
        #    logger.warning(f"用户 {user_id} token余额不足，当前余额: {current_token}，需要扣除: {token_count}")
        #    return False
            
        # 扣除token
        new_token = current_token - token_count
        update_sql = "UPDATE base_sys_user SET token = %s WHERE id = %s"
        val = (new_token, user_id)
        
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(update_sql, val)
                conn.commit()
                affected_rows = cursor.rowcount
                if affected_rows > 0:
                    logger.info(f"成功为用户 {user_id} 扣除 {token_count} 个token，剩余: {new_token}")
                    return True
                else:
                    logger.error(f"更新用户 {user_id} token失败")
                    return False
                    
    except Exception as e:
        logger.error(f"扣除用户 {user_id} token时发生错误: {e}")
        return False

def update_embeddings(file_url, file_name, input_value, type_value, random_uuid):
    logger.info(f"开始向量化处理: url={file_url}, name={file_name}, type={type_value}")
    dashscope.api_key = dashscope_api_key

    # 初始化 dashvector client
    client = Client(
        api_key=dashvector_api_key,
        endpoint=dashvector_endpoint
    )

    vectors = generate_embeddings(file_url, input_value, type_value)
    if not vectors:
        logger.error("数据向量化失败")
        return

    if type_value == 0:
        # 连接集合
        collection = client.get('test')
        logger.info("向图片向量库插入数据")
        ret = collection.insert(
            [
                Doc(
                    id=str(random_uuid),
                    vector=vectors,
                    fields={'title': file_name,
                            'url': file_url,
                            'value': input_value,
                            'id': '',
                            'memo1': str(type_value),
                            'memo2': '',
                            'memo3': '',
                            'memo4': '',
                            'memo5': ''}
                )
            ]
        )
        logger.info(f"插入结果: {ret}")
    elif type_value == 1:
        # 连接集合
        collection = client.get('text_collection')
        logger.info("向文本向量库插入数据")
        ret = collection.insert(
            [
                Doc(
                    id=str(random_uuid),
                    vector=vectors,
                    fields={'title': file_name,
                            'url': '',
                            'value': input_value,
                            'id': '',
                            'memo1': str(type_value),
                            'memo2': '',
                            'memo3': '',
                            'memo4': '',
                            'memo5': ''}
                )
            ]
        )
        logger.info(f"插入结果: {ret}")
    elif type_value == 2:
        # PDF文件类型，不在这里处理向量化
        # PDF的向量化在process_pdf_file函数中分别处理图像理解和OCR结果
        logger.info("PDF文件类型，向量化处理在PDF处理流程中完成")
    logger.info("向量数据库存储成功")

def generate_embeddings(file_url, input_value, type_value):
    logger.info(f"生成向量 embeddings, type: {type_value}")
    if type_value == 0:
        input2 = [{'image': file_url}]
        logger.info("调用多模态embedding模型处理图片")

        # 调用模型接口
        resp = dashscope.MultiModalEmbedding.call(
            model="multimodal-embedding-v1",
            input=input2
        )
        if resp.status_code == HTTPStatus.OK:
            embeddings_list = resp.output['embeddings']
            first_embedding_dict = embeddings_list[0]
            logger.info("图片向量化成功")
            return first_embedding_dict['embedding']
        else:
            logger.error(f"图片向量化失败，状态码: {resp.status_code}")
            return []
    elif type_value == 1:
        client = OpenAI(
            # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
            api_key=dashscope_api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        logger.info("调用文本embedding模型处理文本")
        # 调用模型接口
        resp = client.embeddings.create(
            model="text-embedding-v3",
            input=str(input_value),
            dimensions=1024
        )
        if resp.data:
            logger.info("文本向量化成功")
            return resp.data[0].embedding
        else:
            logger.error("文本向量化失败")
            return []

    return []

def generate_embeddings_v2(text_value, img_url):
    logger.info("生成多模态 embeddings")
    input2 = []
    if img_url:
        input2.append({'factor': 2, 'image': img_url})
    if text_value:
        input2.append({'factor': 1, 'text': text_value})

    logger.info("调用多模态embedding模型处理图文混合内容")
    # 调用模型接口
    resp = dashscope.MultiModalEmbedding.call(
        model="multimodal-embedding-v1",
        input=input2
    )

    if resp.status_code == HTTPStatus.OK:
        embeddings_list = resp.output['embeddings']
        first_embedding_dict = embeddings_list[0]
        logger.info("多模态向量化成功")
        return first_embedding_dict['embedding']
    else:
        logger.error(f"多模态向量化失败，状态码: {resp.status_code}")
        return []

# 提出问题
@app.route("/asking", methods=["POST"])
def asking():
    logger.info("收到提问请求")
    # 初始化 dashvector client
    dashscope.api_key = dashscope_api_key
    # 初始化 dashvector client
    client = Client(
        api_key=dashvector_api_key,
        endpoint=dashvector_endpoint
    )
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    # 解析传参
    param = request.json
    logger.info(f"提问参数: {param}")

    # 定义变量
    answer_back = {"fileName": '', "fileUrl": '', "answer": ''}

    if 'question' in param:
        user_id = param['question'].get('userId', '')
        knowledge_id = param['question'].get('knowledgeId', '')
        
        if 'imgUrl' in param['question']:
            img_url = param['question']['imgUrl']
        else:
            img_url = ''
        if 'questionValue' in param['question']:
            question_value = param['question']['questionValue']
        else:
            if img_url:
                question_value = '你好'
            else:
                question_value = ''
    else:
        user_id = ''
        knowledge_id = ''
        question_value = '你好'
        img_url = ''

    if 'type' in param:
        param_type = param['type']
    else:
        param_type = 1
    
    logger.info(f'提问题传参为，问题：{question_value} 图片地址：{img_url} 类型为：{param_type}')

    # 根据提问内容选择需要查找的向量数据库
    # 修改这一行
    collection_type_result = collection_type_question(question_value)
    collection_type = collection_type_result["answer"] if isinstance(collection_type_result, dict) else collection_type_result
    logger.info(f'需要查找的向量数据库：{collection_type}')
    # 如果是字典对象，则累加token统计
    if isinstance(collection_type_result, dict):
        total_tokens = total_tokens + collection_type_result["total_tokens"]
        prompt_tokens = prompt_tokens + collection_type_result["prompt_tokens"]
        completion_tokens = completion_tokens + collection_type_result["completion_tokens"]
    # 根据type传参来选用不同的模型和结果,type==1向量数据库、type==2LLM模型、type==3两者均有
    if param_type == 1:
        if collection_type == '图片':
            logger.info("查询图片向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("图片向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关图片结果"
        elif collection_type == '文本':
            logger.info("查询文本向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("文本向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关文本结果"
        else:
            logger.info("查询多个向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("多模态向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关结果"
            
            # 连接集合
            #collection2 = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            #rsp2 = collection2.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
            #                       topk=5)
            # 检查第二个查询结果是否有效
            #if rsp2 and rsp2.output:
            #    answer_back['answer'] = rsp2.output[0].fields['value']
            #else:
            #    logger.error("第二个向量库查询未返回有效结果")
            
            # 保存查询记录到数据库
            record_id = insert_question_record(
                question_content=question_value,
                question_type=param_type,
                answer_content=answer_back['answer'],
                question_token=prompt_tokens,
                answer_token=completion_tokens,
                total_token=total_tokens,
                user_id=user_id,
                knowledge_id=knowledge_id,
                question_url=img_url
            )
            answer_back['recordId'] = record_id
            
            # 去base_sys_user表扣除消耗的token
            if user_id and total_tokens > 0:
                # 方式1: 只扣除总token
                deduct_result = deduct_user_token(user_id, total_tokens)
                if deduct_result:
                    logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                    answer_back['token_deducted'] = True
                else:
                    logger.warning(f"为用户 {user_id} 扣除token失败")
                    answer_back['token_deducted'] = False
            
            logger.info(f"返回结果: {answer_back}")
            return answer_back
        
        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back

    elif param_type == 2:
        logger.info("使用LLM直接回答问题")
        if img_url:
            answer = llm_go(question_value, img_url)
        else:
            answer = deepseek_go(question_value)
        logger.info(f'type2问题的答案为: {answer["answer"]}')
        total_tokens = total_tokens + answer["total_tokens"]
        prompt_tokens = prompt_tokens + answer["prompt_tokens"]
        completion_tokens = completion_tokens + answer["completion_tokens"]
        answer_back['answer'] = answer["answer"]
        answer_back['total_tokens'] = total_tokens
        answer_back['prompt_tokens'] = prompt_tokens
        answer_back['completion_tokens'] = completion_tokens
        
        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back
    else:
        logger.info("使用向量检索+LLM组合方式回答问题")
        if collection_type == '图片':
            logger.info("处理图片类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            question_type = 0
            prompt_value = ''
            # 检查查询结果是否有效
            if rsp and rsp.output:
                prompt_url = rsp.output[0].fields['url']
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("图片向量库查询未返回有效结果")
                prompt_url = ''
                answer_back['answer'] = "抱歉，未找到相关图片结果"
        elif collection_type == '文本':
            logger.info("处理文本类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            question_type = 1
            # 检查查询结果是否有效
            if rsp and rsp.output:
                prompt_value = rsp.output[0].fields['value']
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
                prompt_url = ''
            else:
                logger.error("文本向量库查询未返回有效结果")
                prompt_value = ''
                answer_back['answer'] = "抱歉，未找到相关文本结果"
                prompt_url = ''
        else:
            logger.info("处理混合类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
                prompt_value = rsp.output[0].fields['value']
                prompt_url = rsp.output[0].fields['url']
            else:
                logger.error("混合类型向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关结果"
            
            # 连接集合
            #collection2 = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            #rsp2 = collection2.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
            #                       topk=1)
            #question_type = 1
            # 检查第二个查询结果是否有效
            #if rsp2 and rsp2.output:
            #    prompt_value = rsp2.output[0].fields['value']
            #else:
            #   logger.error("第二个向量库查询未返回有效结果")
            #    prompt_value = ''
            
        # 如果向量检索没有返回有效结果，则使用LLM直接回答
        if not answer_back['answer']:
            answer = answer_question(question_type, question_value, prompt_value, prompt_url)
            logger.info(f'type3问题的答案为: {answer["answer"]}')

            total_tokens = total_tokens + answer["total_tokens"]
            prompt_tokens = prompt_tokens + answer["prompt_tokens"]
            completion_tokens = completion_tokens + answer["completion_tokens"]
            answer_back['answer'] = answer["answer"]
        else:
            # 如果向量检索返回了结果，则使用该结果
            answer = {
                "answer": answer_back['answer'],
                "total_tokens": 0,
                "prompt_tokens": 0,
                "completion_tokens": 0
            }

        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back

# 根据提问内容选择需要查找的向量数据库
def collection_type_question(question_value):
    logger.info(f"判断问题类型: {question_value}")
    if question_value:
        prompt = f'''我拥有两个向量数据库，一个是图片数据库，一个是文本数据库，请根据用户提问内容，判断用户希望查找图片还是希望查找文本，请只返回“图片”或者“文本”或者“两种都有”。
            用户问题：{question_value}
            '''
        result = deepseek_go(prompt)
        logger.info(f"问题类型判断结果: {result}")
        return result

# 构造 Prompt
def answer_question(question_type, question_value, prompt_value, img_url):
    logger.info(f"构建回答问题的Prompt: type={question_type}, question={question_value}")
    if question_type == 1:
        if question_value:
            prompt = f'''请基于```内的内容回答问题。"
                ```
                {prompt_value}
                ```
                我的问题是：{question_value}。
                '''
            return llm_go(prompt, '')
        else:
            return prompt_value
    else:
        prompt = '请描述图片内容'
        return llm_go(prompt, img_url)

# GPT方法,向LLM提问
def llm_go(question_value, img_url):
    logger.info(f"调用Qwen模型回答问题: {question_value}")
    client = OpenAI(
        # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=dashscope_api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    content_value = []
    if question_value:
        text_value = {"type": "text", "text": question_value}
        content_value.append(text_value)
    if img_url:
        img_value = {"type": "image_url", "image_url": img_url}
        content_value.append(img_value)
    
    logger.info("开始调用Qwen模型")
    # 调用llm模型
    completion = client.chat.completions.create(
        model="qwen-omni-turbo",
        messages=[
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are a helpful assistant."}],
            },
            {
                "role": "user",
                "content": content_value,
            },
        ],
        # 设置输出数据的模态，当前支持两种：["text","audio"]、["text"]
        modalities=["text"],
        # stream 必须设置为 True，否则会报错
        stream=True,
        stream_options={
            "include_usage": True
        }
    )
    logger.info("Qwen模型调用成功，开始接收流式响应")
    answer = ''
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    
    for chunk in completion:
        if chunk.choices:
            if chunk.choices[0].delta.content:
                answer = answer + chunk.choices[0].delta.content
        elif chunk.usage:
            # 记录token消耗量
            total_tokens = chunk.usage.total_tokens
            prompt_tokens = chunk.usage.prompt_tokens
            completion_tokens = chunk.usage.completion_tokens
            logger.info(f"Qwen模型Token消耗 - 总计: {total_tokens}, 提示: {prompt_tokens}, 完成: {completion_tokens}")

    logger.info(f"Qwen模型回答完成，总token消耗: {total_tokens}")
    data = {"answer": answer, "total_tokens": total_tokens, "prompt_tokens": prompt_tokens, "completion_tokens": completion_tokens}
    return data

# GPT方法,向deepseek提问
def deepseek_go(question_value):
    logger.info(f"调用DeepSeek模型回答问题: {question_value}")
    client = OpenAI(
        # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=dashscope_api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    logger.info("开始调用DeepSeek模型")
    # 调用llm模型
    completion = client.chat.completions.create(
        model="deepseek-v3",
        messages=[
            {
                "role": "user",
                "content": question_value
            }
        ]
    )

    logger.info("DeepSeek模型调用成功")
    answer = completion.choices[0].message.content
    
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    # 输出token消耗量
    if hasattr(completion, 'usage') and completion.usage:
        total_tokens = completion.usage.total_tokens
        prompt_tokens = completion.usage.prompt_tokens
        completion_tokens = completion.usage.completion_tokens
        logger.info(f"DeepSeek模型Token消耗 - 总计: {total_tokens}, 提示: {prompt_tokens}, 完成: {completion_tokens}")
    
    logger.info(f"DeepSeek模型回答完成")
    data = {"answer": answer, "total_tokens": total_tokens, "prompt_tokens": prompt_tokens, "completion_tokens": completion_tokens}
    return data

if __name__ == '__main__':
    logger.info("应用启动中...")
    try:
        pool = create_conn_pool()
        logger.info("数据库连接池创建成功")
    except Exception as e:
        logger.warning(f"数据库连接失败，将在测试模式下运行: {e}")
        pool = None

    logger.info("应用启动完成，监听端口 18052")
    app.run(host="0.0.0.0", port=18052)