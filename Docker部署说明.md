# PDF文档处理和向量化存储系统 - Docker部署说明

## 概述
本文档详细说明如何使用Docker部署PDF文档处理和向量化存储系统。

## 前置要求
- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

## 部署步骤

### 1. 准备部署文件
确保以下文件在同一目录中：
```
project/
├── newDashVector.py          # 主应用文件
├── requirements.txt          # Python依赖
├── Dockerfile               # Docker镜像构建文件
├── docker-compose.yml       # Docker Compose配置
├── nginx.conf              # Nginx配置（可选）
└── sql/                    # 数据库初始化脚本（可选）
    └── init.sql
```

### 2. 配置环境变量
编辑 `docker-compose.yml` 文件，修改以下配置：

```yaml
environment:
  # 数据库配置
  - DB_HOST=your_db_host
  - DB_PORT=3306
  - DB_USER=your_db_user
  - DB_PASSWORD=your_db_password
  - DB_NAME=your_db_name
  
  # 阿里云API配置
  - DASHSCOPE_API_KEY=your_dashscope_api_key
  - DASHVECTOR_API_KEY=your_dashvector_api_key
  - DASHVECTOR_ENDPOINT=your_dashvector_endpoint
```

### 3. 构建和启动服务

#### 方式一：使用Docker Compose（推荐）
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f pdf-rag-app
```

#### 方式二：单独构建Docker镜像
```bash
# 构建镜像
docker build -t pdf-rag-app .

# 运行容器
docker run -d \
  --name pdf-rag-app \
  -p 18052:18052 \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/temp:/app/temp \
  -e DASHSCOPE_API_KEY=your_api_key \
  pdf-rag-app
```

### 4. 验证部署
```bash
# 检查服务健康状态
curl http://localhost:18052/

# 测试上传接口
curl -X POST http://localhost:18052/upload \
  -H "Content-Type: application/json" \
  -d '{"type": 1, "inputValue": "测试文本"}'
```

## 服务配置说明

### 主应用服务 (pdf-rag-app)
- **端口**: 18052
- **功能**: PDF处理、向量化存储、问答服务
- **资源要求**: 2GB内存，2CPU核心

### MySQL数据库服务 (mysql)
- **端口**: 3306
- **功能**: 存储文件信息、处理记录
- **数据持久化**: mysql_data卷

### Redis缓存服务 (redis)
- **端口**: 6379
- **功能**: 缓存向量结果、会话管理
- **数据持久化**: redis_data卷

### Nginx代理服务 (nginx)
- **端口**: 80, 443
- **功能**: 反向代理、负载均衡、SSL终止

## 目录结构说明

### 应用目录
```
/app/
├── newDashVector.py         # 主应用文件
├── logs/                   # 日志目录
├── temp/                   # 临时文件目录
└── models/                 # 模型缓存目录
```

### 数据卷挂载
```
./logs -> /app/logs         # 日志持久化
./temp -> /app/temp         # 临时文件访问
./models -> /app/models     # 模型缓存持久化
```

## 常用操作命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart pdf-rag-app

# 查看服务状态
docker-compose ps

# 查看资源使用情况
docker stats
```

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f pdf-rag-app

# 查看最近100行日志
docker-compose logs --tail=100 pdf-rag-app

# 查看错误日志
docker-compose logs pdf-rag-app | grep ERROR
```

### 数据管理
```bash
# 备份MySQL数据
docker exec pdf-rag-mysql mysqldump -u root -p cool-admin > backup.sql

# 恢复MySQL数据
docker exec -i pdf-rag-mysql mysql -u root -p cool-admin < backup.sql

# 清理临时文件
docker exec pdf-rag-app rm -rf /app/temp/*
```

## 性能优化

### 1. 资源限制
在 `docker-compose.yml` 中添加资源限制：
```yaml
services:
  pdf-rag-app:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
        reservations:
          memory: 2G
          cpus: '1'
```

### 2. 存储优化
```bash
# 使用SSD存储挂载临时目录
volumes:
  - /fast-storage/temp:/app/temp
```

### 3. 网络优化
```bash
# 使用host网络模式（生产环境谨慎使用）
network_mode: host
```

## 故障排除

### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs pdf-rag-app

# 检查配置文件语法
docker-compose config
```

### 2. 内存不足
```bash
# 增加Docker内存限制
# 或者调整应用配置减少内存使用
```

### 3. 端口冲突
```bash
# 修改docker-compose.yml中的端口映射
ports:
  - "18053:18052"  # 使用不同的主机端口
```

### 4. 权限问题
```bash
# 修复文件权限
sudo chown -R 1000:1000 ./logs ./temp ./models
```

## 生产环境建议

1. **安全配置**
   - 使用环境变量文件存储敏感信息
   - 配置SSL证书
   - 限制网络访问

2. **监控配置**
   - 集成Prometheus监控
   - 配置日志收集
   - 设置告警规则

3. **备份策略**
   - 定期备份数据库
   - 备份模型文件
   - 备份配置文件

4. **扩展配置**
   - 使用Docker Swarm或Kubernetes
   - 配置负载均衡
   - 实现自动扩缩容
