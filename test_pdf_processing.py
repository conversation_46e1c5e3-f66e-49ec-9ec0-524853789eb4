#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文档处理和向量化存储系统测试脚本
"""

import requests
import json
import time
import os

# 测试配置
BASE_URL = "http://localhost:18052"
TEST_FILES = {
    "pdf_url": "https://example.com/test.pdf",  # 替换为实际的PDF文件URL
    "image_url": "https://example.com/test.jpg",  # 替换为实际的图片文件URL
    "text_content": "这是一个测试文本，用于验证文本向量化功能。"
}

def test_upload_text():
    """测试文本上传功能"""
    print("=" * 50)
    print("测试文本上传功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/upload"
    data = {
        "type": 1,
        "inputValue": TEST_FILES["text_content"]
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 文本上传测试成功")
            return True
        else:
            print("✗ 文本上传测试失败")
            return False
    except Exception as e:
        print(f"✗ 文本上传测试异常: {e}")
        return False

def test_upload_image():
    """测试图片上传功能"""
    print("=" * 50)
    print("测试图片上传功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/upload"
    data = {
        "type": 0,
        "url": TEST_FILES["image_url"],
        "imageName": "test_image.jpg"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 图片上传测试成功")
            return True
        else:
            print("✗ 图片上传测试失败")
            return False
    except Exception as e:
        print(f"✗ 图片上传测试异常: {e}")
        return False

def test_upload_pdf():
    """测试PDF上传功能"""
    print("=" * 50)
    print("测试PDF上传功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/upload"
    data = {
        "type": 2,
        "url": TEST_FILES["pdf_url"],
        "imageName": "test_document.pdf"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ PDF上传测试成功")
            print(f"检测到的文件: {result.get('detected_files', [])}")
            return True
        else:
            print("✗ PDF上传测试失败")
            return False
    except Exception as e:
        print(f"✗ PDF上传测试异常: {e}")
        return False

def test_auto_detection():
    """测试自动文件检测功能"""
    print("=" * 50)
    print("测试自动文件检测功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/upload"
    
    # 测试包含PDF链接的文本
    test_cases = [
        {
            "name": "包含PDF链接的文本",
            "data": {
                "inputValue": f"请处理这个PDF文件：{TEST_FILES['pdf_url']}"
            }
        },
        {
            "name": "包含图片链接的文本", 
            "data": {
                "inputValue": f"请分析这张图片：{TEST_FILES['image_url']}"
            }
        },
        {
            "name": "纯文本内容",
            "data": {
                "inputValue": "这是一段纯文本，不包含任何文件链接。"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"响应: {result}")
                print(f"检测到的文件: {result.get('detected_files', [])}")
                print("✓ 测试成功")
            else:
                print(f"✗ 测试失败: {response.text}")
        except Exception as e:
            print(f"✗ 测试异常: {e}")

def test_asking():
    """测试问答功能"""
    print("=" * 50)
    print("测试问答功能")
    print("=" * 50)
    
    url = f"{BASE_URL}/asking"
    data = {
        "question": {
            "questionValue": "请介绍一下系统的功能",
            "collectionName": "test",
            "userId": "test_user",
            "knowledgeId": "test_knowledge"
        },
        "type": 2  # 使用LLM直接回答
    }
    
    try:
        response = requests.post(url, json=data, timeout=60)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"回答: {result.get('answer', '无回答')}")
            print("✓ 问答测试成功")
            return True
        else:
            print(f"✗ 问答测试失败: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 问答测试异常: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    print("=" * 50)
    print("测试服务健康状态")
    print("=" * 50)
    
    try:
        response = requests.get(BASE_URL, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ 服务运行正常")
            return True
        else:
            print("✗ 服务状态异常")
            return False
    except Exception as e:
        print(f"✗ 服务连接失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行PDF文档处理系统测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 健康检查
    test_results.append(("健康检查", test_health_check()))
    
    # 等待服务完全启动
    time.sleep(2)
    
    # 功能测试
    test_results.append(("文本上传", test_upload_text()))
    time.sleep(1)
    
    test_results.append(("图片上传", test_upload_image()))
    time.sleep(1)
    
    test_results.append(("PDF上传", test_upload_pdf()))
    time.sleep(1)
    
    # 自动检测测试
    test_auto_detection()
    time.sleep(1)
    
    test_results.append(("问答功能", test_asking()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置和日志。")

if __name__ == "__main__":
    run_all_tests()
