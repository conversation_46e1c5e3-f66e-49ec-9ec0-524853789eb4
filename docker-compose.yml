# PDF文档处理和向量化存储系统 Docker Compose配置
version: '3.8'

services:
  # 主应用服务
  pdf-rag-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pdf-rag-app
    ports:
      - "18052:18052"
    environment:
      # 应用配置
      - PYTHONUNBUFFERED=1
      - FLASK_ENV=production
      # 数据库配置（根据实际情况修改）
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=peilian123a!
      - DB_NAME=cool-admin
      # 阿里云API配置（请替换为实际的API Key）
      - DASHSCOPE_API_KEY=sk-6f882d82aa9645c1a799e95c165a86c6
      - DASHVECTOR_API_KEY=sk-UeyJDmZHk6HD5rPfRQv6PZ6301q0Q1C1C091F8A9A11EEBF5562E118F409F5
      - DASHVECTOR_ENDPOINT=vrs-cn-0mm44scjt000bq.dashvector.cn-beijing.aliyuncs.com
    volumes:
      # 日志目录
      - ./logs:/app/logs
      # 临时文件目录
      - ./temp:/app/temp
      # 模型缓存目录
      - ./models:/app/models
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - pdf-rag-network

  # MySQL数据库服务（可选，如果使用外部数据库可以移除）
  mysql:
    image: mysql:8.0
    container_name: pdf-rag-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=peilian123a!
      - MYSQL_DATABASE=cool-admin
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      # 数据持久化
      - mysql_data:/var/lib/mysql
      # 初始化脚本
      - ./sql:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - pdf-rag-network

  # Redis缓存服务（可选，用于缓存向量结果）
  redis:
    image: redis:7-alpine
    container_name: pdf-rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - pdf-rag-network

  # Nginx反向代理（可选，用于生产环境）
  nginx:
    image: nginx:alpine
    container_name: pdf-rag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - pdf-rag-app
    restart: unless-stopped
    networks:
      - pdf-rag-network

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

# 网络配置
networks:
  pdf-rag-network:
    driver: bridge
