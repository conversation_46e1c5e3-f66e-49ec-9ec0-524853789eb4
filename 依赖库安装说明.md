# PDF文档处理和向量化存储系统 - 依赖库安装说明

## 概述
本系统基于现有的 `newDashVector.py` 文件扩展，新增了PDF文档处理功能，包括PDF转图片、图像理解、OCR文字识别和向量化存储等功能。

## 系统要求
- Python 3.8+
- 操作系统：Windows/Linux/macOS
- 内存：建议4GB以上
- 磁盘空间：建议10GB以上（用于模型文件和临时文件）

## 依赖库安装

### 1. 基础依赖库（原有系统已包含）
```bash
# Flask Web框架
pip install Flask==2.3.3

# 阿里云DashScope SDK
pip install dashscope==1.17.0

# 阿里云DashVector向量数据库SDK
pip install dashvector==1.0.1

# MySQL数据库连接
pip install pymysql==1.1.0
pip install DBUtils==3.0.3

# OpenAI兼容接口
pip install openai==1.3.7
```

### 2. PDF处理相关依赖库
```bash
# PDF转图片核心库
pip install pdf2image==1.16.3

# PDF处理备用库（PyMuPDF）
pip install PyMuPDF==1.23.8

# Poppler工具（PDF处理底层依赖）
# Windows用户：
pip install poppler-utils==0.1.0
# 或者下载Poppler二进制文件：https://github.com/oschwartz10612/poppler-windows/releases/

# Linux用户：
sudo apt-get install poppler-utils

# macOS用户：
brew install poppler
```

### 3. OCR文字识别依赖库
```bash
# PaddleOCR文字识别库
pip install paddleocr==2.7.3

# PaddlePaddle深度学习框架
pip install paddlepaddle==2.5.2
# 如果有GPU，可以安装GPU版本：
# pip install paddlepaddle-gpu==2.5.2
```

### 4. 图像处理依赖库
```bash
# Python图像处理库
pip install Pillow==10.1.0

# OpenCV图像处理库
pip install opencv-python==********
```

### 5. 网络和工具依赖库
```bash
# HTTP请求库
pip install requests==2.31.0

# 路径处理工具
pip install pathlib2==2.3.7
```

## 一键安装命令
```bash
# 使用requirements.txt一键安装所有依赖
pip install -r requirements.txt
```

## 各依赖库用途说明

### PDF处理模块
- **pdf2image**: 将PDF文件转换为图片格式，支持多页PDF处理
- **PyMuPDF**: PDF处理的备用方案，提供更好的性能和兼容性
- **poppler-utils**: PDF处理的底层工具，pdf2image的依赖

### OCR识别模块
- **paddleocr**: 百度开源的OCR文字识别库，支持中英文识别
- **paddlepaddle**: PaddleOCR的深度学习框架依赖

### 图像处理模块
- **Pillow**: Python图像处理库，用于图片格式转换和处理
- **opencv-python**: 计算机视觉库，提供图像处理功能

### 网络和工具模块
- **requests**: HTTP请求库，用于下载网络文件
- **pathlib2**: 路径处理工具，提供跨平台文件路径操作

## 安装验证
安装完成后，可以运行以下Python代码验证安装：

```python
# 验证PDF处理
try:
    from pdf2image import convert_from_path
    print("✓ pdf2image 安装成功")
except ImportError:
    print("✗ pdf2image 安装失败")

# 验证OCR功能
try:
    from paddleocr import PaddleOCR
    print("✓ PaddleOCR 安装成功")
except ImportError:
    print("✗ PaddleOCR 安装失败")

# 验证图像处理
try:
    from PIL import Image
    import cv2
    print("✓ 图像处理库安装成功")
except ImportError:
    print("✗ 图像处理库安装失败")
```

## 常见问题解决

### 1. pdf2image安装问题
如果遇到 "poppler not found" 错误：
- Windows: 下载Poppler二进制文件并添加到PATH
- Linux: `sudo apt-get install poppler-utils`
- macOS: `brew install poppler`

### 2. PaddleOCR安装问题
如果下载模型文件缓慢：
```bash
# 设置PaddleOCR模型下载源
export HUB_HOME=/path/to/your/model/cache
```

### 3. 内存不足问题
如果处理大型PDF时内存不足：
- 增加系统内存
- 调整PDF转图片的DPI设置
- 分批处理PDF页面

## 性能优化建议
1. 使用SSD硬盘提高文件I/O性能
2. 配置GPU加速PaddleOCR（如果有GPU）
3. 调整临时文件目录到高速存储设备
4. 根据服务器配置调整并发处理数量
