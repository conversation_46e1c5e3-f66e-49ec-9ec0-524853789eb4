#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF处理功能测试服务器
简化版本，专门用于测试PDF处理功能
"""

import json
from flask import Flask, request
import uuid
from threading import Thread
import tempfile
import shutil
import os
import requests
from urllib.parse import urlparse
from pathlib import Path
import re
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 文件类型检测函数
def detect_file_type(file_path_or_url):
    """检测文件类型"""
    try:
        logger.info(f"检测文件类型: {file_path_or_url}")
        
        if file_path_or_url.startswith(('http://', 'https://')):
            parsed_url = urlparse(file_path_or_url)
            file_path = parsed_url.path
        else:
            file_path = file_path_or_url
        
        file_extension = Path(file_path).suffix.lower()
        logger.info(f"文件扩展名: {file_extension}")
        
        if file_extension == '.pdf':
            return 'pdf'
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            return 'image'
        elif file_extension in ['.txt', '.md', '.doc', '.docx']:
            return 'text'
        else:
            if re.search(r'\.pdf(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'pdf'
            elif re.search(r'\.(jpg|jpeg|png|bmp|gif|tiff|webp)(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'image'
            else:
                return 'unknown'
                
    except Exception as e:
        logger.error(f"文件类型检测失败: {e}")
        return 'unknown'

# 检测输入中是否包含文件链接或路径
def detect_file_in_input(input_text):
    """检测输入文本中是否包含文件链接或路径"""
    try:
        if not input_text:
            return False, []
        
        logger.info(f"检测输入中的文件链接: {input_text}")
        
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+\.(pdf|jpg|jpeg|png|bmp|gif|tiff|webp|txt|md|doc|docx)'
        path_pattern = r'[A-Za-z]:[\\\/][^\s<>"{}|\\^`\[\]]*\.(pdf|jpg|jpeg|png|bmp|gif|tiff|webp|txt|md|doc|docx)'
        
        file_paths = []
        for match in re.finditer(url_pattern, input_text, re.IGNORECASE):
            file_paths.append(match.group(0))
        for match in re.finditer(path_pattern, input_text, re.IGNORECASE):
            file_paths.append(match.group(0))
        
        has_files = len(file_paths) > 0
        logger.info(f"检测结果: 包含文件={has_files}, 文件列表={file_paths}")
        
        return has_files, file_paths
        
    except Exception as e:
        logger.error(f"文件链接检测失败: {e}")
        return False, []

# 下载或复制文件到临时目录
def download_or_copy_file(file_url, temp_dir):
    """下载URL文件或复制本地文件到临时目录"""
    try:
        if file_url.startswith(('http://', 'https://')):
            logger.info(f"下载网络文件: {file_url}")
            response = requests.get(file_url, timeout=30)
            response.raise_for_status()
            
            file_name = Path(urlparse(file_url).path).name
            if not file_name or '.' not in file_name:
                file_name = 'downloaded_file.pdf'
            
            temp_file_path = os.path.join(temp_dir, file_name)
            with open(temp_file_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"文件下载完成: {temp_file_path}")
            return temp_file_path
        else:
            logger.info(f"复制本地文件: {file_url}")
            if os.path.exists(file_url):
                file_name = Path(file_url).name
                temp_file_path = os.path.join(temp_dir, file_name)
                shutil.copy2(file_url, temp_file_path)
                logger.info(f"文件复制完成: {temp_file_path}")
                return temp_file_path
            else:
                logger.error(f"本地文件不存在: {file_url}")
                return None
                
    except Exception as e:
        logger.error(f"文件下载或复制失败: {e}")
        return None

# PDF转图片函数
def convert_pdf_to_images(pdf_path, output_dir):
    """将PDF文件转换为图片"""
    try:
        logger.info(f"开始将PDF转换为图片: {pdf_path}")
        
        try:
            import fitz  # PyMuPDF
            logger.info("使用PyMuPDF进行PDF转换")
            
            pdf_document = fitz.open(pdf_path)
            image_paths = []
            
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)
                
                image_filename = f"page_{page_num + 1:03d}.png"
                image_path = os.path.join(output_dir, image_filename)
                pix.save(image_path)
                image_paths.append(image_path)
                logger.info(f"保存第 {page_num + 1} 页图片: {image_path}")
            
            pdf_document.close()
            logger.info(f"PDF转换完成，共生成 {len(image_paths)} 张图片")
            return image_paths
            
        except ImportError:
            logger.error("PyMuPDF库未安装")
            return []
            
    except Exception as e:
        logger.error(f"PDF转图片失败: {e}")
        return []

# PDF文件处理函数
def process_pdf_file(pdf_url, pdf_name, file_uuid):
    """处理PDF文件：转换为图片"""
    logger.info(f"开始处理PDF文件: {pdf_url}")
    temp_dir = None
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='pdf_processing_')
        logger.info(f"创建临时目录: {temp_dir}")
        
        # 下载或读取PDF文件
        pdf_path = download_or_copy_file(pdf_url, temp_dir)
        if not pdf_path:
            logger.error("PDF文件下载或复制失败")
            return {"success": False, "error": "PDF文件下载失败"}
        
        # 将PDF转换为图片
        image_paths = convert_pdf_to_images(pdf_path, temp_dir)
        if not image_paths:
            logger.error("PDF转图片失败")
            return {"success": False, "error": "PDF转图片失败"}
        
        logger.info(f"PDF转换完成，共生成 {len(image_paths)} 张图片")
        
        # 模拟处理每一页图片
        processed_pages = []
        for page_num, image_path in enumerate(image_paths, 1):
            logger.info(f"处理第 {page_num} 页图片: {image_path}")
            
            # 获取图片文件大小
            image_size = os.path.getsize(image_path)
            
            processed_pages.append({
                "page_number": page_num,
                "image_path": image_path,
                "image_size": image_size,
                "status": "processed"
            })
        
        logger.info(f"PDF文件 {pdf_name} 处理完成")
        return {
            "success": True,
            "total_pages": len(image_paths),
            "processed_pages": processed_pages
        }
        
    except Exception as e:
        logger.error(f"PDF文件处理过程中发生错误: {e}")
        return {"success": False, "error": str(e)}
    finally:
        # 清理临时文件
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"清理临时目录: {temp_dir}")
            except Exception as e:
                logger.error(f"清理临时目录失败: {e}")

# 处理上传任务
def do_upload(param_url, param_image_name, param_input_value, type_value, detected_files=None):
    """执行上传任务"""
    logger.info(f"开始执行上传任务: url={param_url}, name={param_image_name}, value={param_input_value}, type={type_value}")
    random_uuid = uuid.uuid4()
    
    if type_value == 2:  # PDF文件
        logger.info("处理PDF文件")
        result = process_pdf_file(param_url, param_image_name, random_uuid)
        logger.info(f"PDF处理结果: {result}")
    else:
        logger.info(f"其他类型文件处理: type={type_value}")
    
    logger.info("文件处理任务完成")

# 上传接口
@app.route("/upload", methods=["POST"])
def upload():
    logger.info("开始上传文件或文字")
    
    # 解析传参
    param = request.json
    logger.info(f"接收到参数: {param}")
    param_url = ''
    param_image_name = ''
    param_input_value = ''
    type_value = param.get('type', 1)
    
    if 'url' in param:
        param_url = param['url']
    if 'imageName' in param:
        param_image_name = param['imageName']
    if 'inputValue' in param:
        param_input_value = param['inputValue']
    
    # 智能检测文件类型
    detected_files = []
    if param_url:
        file_type = detect_file_type(param_url)
        if file_type == 'pdf':
            type_value = 2
        elif file_type == 'image':
            type_value = 0
        logger.info(f"URL文件类型检测结果: {file_type}, type_value: {type_value}")
    
    if param_input_value:
        has_files, file_paths = detect_file_in_input(param_input_value)
        if has_files:
            detected_files = file_paths
            first_file = file_paths[0]
            file_type = detect_file_type(first_file)
            if file_type == 'pdf':
                type_value = 2
                param_url = first_file
                if not param_image_name:
                    param_image_name = Path(first_file).name
            elif file_type == 'image':
                type_value = 0
                param_url = first_file
                if not param_image_name:
                    param_image_name = Path(first_file).name
            logger.info(f"输入文本中检测到文件: {first_file}, 类型: {file_type}")
    
    logger.info(f"准备启动线程处理上传任务，参数: url={param_url}, name={param_image_name}, type={type_value}")
    t = Thread(target=do_upload, args=(param_url, param_image_name, param_input_value, type_value, detected_files))
    t.start()

    return {'status': 'ok', 'message': '文件处理任务已启动', 'detected_files': detected_files, 'type': type_value}

# 健康检查接口
@app.route("/", methods=["GET"])
def health_check():
    return {'status': 'ok', 'message': 'PDF处理测试服务运行正常'}

if __name__ == '__main__':
    logger.info("PDF处理测试服务启动中...")
    logger.info("测试服务启动完成，监听端口 18052")
    app.run(host="0.0.0.0", port=18052, debug=True)
