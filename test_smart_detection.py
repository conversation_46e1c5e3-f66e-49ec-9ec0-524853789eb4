#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能文件检测功能
"""

import requests
import json

def test_smart_detection():
    """测试智能文件检测功能"""
    print("=" * 60)
    print("测试智能文件检测功能")
    print("=" * 60)
    
    url = "http://localhost:18052/upload"
    
    test_cases = [
        {
            "name": "包含PDF链接的文本",
            "data": {
                "inputValue": "请处理这个PDF文件：http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf"
            },
            "expected_type": 2
        },
        {
            "name": "包含图片链接的文本",
            "data": {
                "inputValue": "请分析这张图片：https://example.com/image.jpg"
            },
            "expected_type": 0
        },
        {
            "name": "纯文本内容",
            "data": {
                "inputValue": "这是一段纯文本，不包含任何文件链接。"
            },
            "expected_type": 1
        },
        {
            "name": "直接指定PDF URL",
            "data": {
                "type": 2,
                "url": "http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf",
                "imageName": "test.pdf"
            },
            "expected_type": 2
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['name']}")
        print(f"输入数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=30)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                actual_type = result.get('type', 1)
                detected_files = result.get('detected_files', [])
                
                print(f"检测到的文件类型: {actual_type}")
                print(f"检测到的文件列表: {detected_files}")
                
                # 检查结果是否符合预期
                if actual_type == test_case['expected_type']:
                    print("✅ 测试通过")
                    results.append(True)
                else:
                    print(f"❌ 测试失败 - 期望类型: {test_case['expected_type']}, 实际类型: {actual_type}")
                    results.append(False)
            else:
                print(f"❌ 请求失败: {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            results.append(False)
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_case, result) in enumerate(zip(test_cases, results), 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"测试用例 {i} ({test_case['name']}): {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有智能检测测试通过！")
    else:
        print("⚠️  部分测试失败，请检查实现。")
    
    return passed == total

if __name__ == "__main__":
    test_smart_detection()
