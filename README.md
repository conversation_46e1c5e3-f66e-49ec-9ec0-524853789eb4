# PDF文档处理和向量化存储系统

## 项目概述

本项目基于现有的 `newDashVector.py` 文件扩展，实现了一个完整的PDF文档处理和向量化存储系统。系统能够智能检测输入中的文件链接，对PDF文件进行处理，包括转换为图片、图像理解、OCR文字识别，并将结果向量化存储到向量数据库中。

## 主要功能

### 🔍 智能文件检测
- 自动检测输入中的文件链接或文件路径
- 支持URL和本地路径识别
- 智能判断文件类型（PDF、图片、文本）

### 📄 PDF文档处理
- **PDF转图片**: 使用pdf2image库将PDF每页转换为高质量图片
- **图像理解**: 集成阿里云通义千问VL模型，生成图片语义描述
- **OCR文字识别**: 使用PaddleOCR提取图片中的文字内容
- **向量化存储**: 分别将图像理解结果和OCR文本转换为向量存储

### 💾 数据存储策略
- **原始文件**: 只保存文件路径/链接，不持久化中间文件
- **向量存储**: 图像理解向量和OCR文本向量分别存储
- **关联信息**: 通过UUID关联原始文件和处理结果

### 🔧 系统特性
- **临时文件管理**: 自动清理处理过程中的临时图片文件
- **错误处理**: 完善的异常处理和日志记录
- **状态跟踪**: 实时跟踪PDF处理状态
- **多格式支持**: 支持多种文件格式的智能识别

## 技术架构

### 核心技术栈
- **Web框架**: Flask
- **向量数据库**: 阿里云DashVector
- **AI模型**: 阿里云DashScope (通义千问)
- **PDF处理**: pdf2image, PyMuPDF
- **OCR识别**: PaddleOCR
- **图像处理**: Pillow, OpenCV
- **数据库**: MySQL

### 处理流程
```
输入检测 → 文件类型判断 → PDF转图片 → 并行处理
                                    ├── 图像理解 → 向量化存储
                                    └── OCR识别 → 向量化存储
```

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd pdf-rag-system

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置
编辑 `newDashVector.py` 中的配置：
```python
# 阿里云API配置
dashscope_api_key = 'your_dashscope_api_key'
dashvector_api_key = 'your_dashvector_api_key'
dashvector_endpoint = 'your_dashvector_endpoint'

# 数据库配置
host = 'your_mysql_host'
user = 'your_mysql_user'
password = 'your_mysql_password'
database = 'your_database_name'
```

### 3. 启动服务
```bash
python newDashVector.py
```

### 4. 测试系统
```bash
python test_pdf_processing.py
```

## API接口说明

### 上传接口 `/upload`
**POST** 请求，支持多种输入方式：

#### 1. PDF文件处理
```json
{
    "type": 2,
    "url": "https://example.com/document.pdf",
    "imageName": "document.pdf"
}
```

#### 2. 图片文件处理
```json
{
    "type": 0,
    "url": "https://example.com/image.jpg",
    "imageName": "image.jpg"
}
```

#### 3. 文本内容处理
```json
{
    "type": 1,
    "inputValue": "这是要处理的文本内容"
}
```

#### 4. 智能检测模式
```json
{
    "inputValue": "请处理这个PDF文件：https://example.com/document.pdf"
}
```

### 问答接口 `/asking`
**POST** 请求，支持向量检索和LLM问答：

```json
{
    "question": {
        "questionValue": "问题内容",
        "collectionName": "向量集合名称",
        "userId": "用户ID",
        "knowledgeId": "知识库ID"
    },
    "type": 1  // 1-向量检索, 2-LLM直答, 3-混合模式
}
```

## Docker部署

### 1. 使用Docker Compose（推荐）
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f pdf-rag-app
```

### 2. 单独构建镜像
```bash
# 构建镜像
docker build -t pdf-rag-app .

# 运行容器
docker run -d -p 18052:18052 pdf-rag-app
```

详细部署说明请参考 [Docker部署说明.md](Docker部署说明.md)

## 文件结构

```
project/
├── newDashVector.py              # 主应用文件
├── requirements.txt              # Python依赖
├── Dockerfile                   # Docker镜像构建
├── docker-compose.yml           # Docker Compose配置
├── test_pdf_processing.py       # 测试脚本
├── 依赖库安装说明.md             # 依赖安装指南
├── Docker部署说明.md            # Docker部署指南
└── README.md                    # 项目说明文档
```

## 依赖库说明

### PDF处理相关
- `pdf2image`: PDF转图片核心库
- `PyMuPDF`: PDF处理备用方案
- `poppler-utils`: PDF处理底层工具

### OCR识别相关
- `paddleocr`: 百度开源OCR库
- `paddlepaddle`: 深度学习框架

### 图像处理相关
- `Pillow`: Python图像处理库
- `opencv-python`: 计算机视觉库

详细安装说明请参考 [依赖库安装说明.md](依赖库安装说明.md)

## 性能优化建议

1. **硬件配置**
   - 使用SSD硬盘提高I/O性能
   - 配置GPU加速OCR处理
   - 增加内存以支持大文件处理

2. **软件配置**
   - 调整PDF转图片的DPI设置
   - 配置模型缓存目录
   - 优化数据库连接池

3. **部署优化**
   - 使用负载均衡分散请求
   - 配置Redis缓存向量结果
   - 实现异步处理队列

## 故障排除

### 常见问题
1. **pdf2image安装失败**: 需要安装poppler工具
2. **PaddleOCR模型下载慢**: 配置国内镜像源
3. **内存不足**: 调整处理参数或增加内存
4. **临时文件堆积**: 检查清理机制是否正常

### 日志查看
```bash
# 查看应用日志
tail -f app.log

# 查看Docker日志
docker-compose logs -f pdf-rag-app
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详情请参考 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 技术交流群
