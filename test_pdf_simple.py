#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PDF处理测试，不依赖数据库和向量数据库
"""

import tempfile
import os
import shutil
import requests
from pathlib import Path

def test_pdf_download_and_convert():
    """测试PDF下载和转换功能"""
    print("=" * 50)
    print("测试PDF下载和转换功能")
    print("=" * 50)
    
    # PDF文件URL
    pdf_url = "http://biligpt.oss-cn-beijing.aliyuncs.com/file/1758524300215144_AWS%20Certified%20Solutions%20Architect%20-%20Professional.pdf"
    temp_dir = None
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='pdf_test_')
        print(f"创建临时目录: {temp_dir}")
        
        # 下载PDF文件
        print("开始下载PDF文件...")
        response = requests.get(pdf_url, timeout=30)
        response.raise_for_status()
        
        # 生成临时文件名
        file_name = Path(pdf_url.split('/')[-1]).name
        if not file_name or '.' not in file_name:
            file_name = 'downloaded_file.pdf'
        
        pdf_path = os.path.join(temp_dir, file_name)
        with open(pdf_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ PDF文件下载完成: {pdf_path}")
        print(f"文件大小: {len(response.content)} 字节")
        
        # 测试PyMuPDF转换
        print("\n开始PDF转图片测试...")
        image_paths = convert_pdf_to_images_test(pdf_path, temp_dir)
        
        if image_paths:
            print(f"✓ PDF转换成功，共生成 {len(image_paths)} 张图片")
            for i, img_path in enumerate(image_paths, 1):
                img_size = os.path.getsize(img_path)
                print(f"  第{i}页: {img_path} ({img_size} 字节)")
        else:
            print("✗ PDF转换失败")
        
        return len(image_paths) > 0
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"\n✓ 临时文件清理完成: {temp_dir}")
            except Exception as e:
                print(f"✗ 临时文件清理失败: {e}")

def convert_pdf_to_images_test(pdf_path, output_dir):
    """测试PDF转图片功能"""
    try:
        print(f"使用PyMuPDF转换PDF: {pdf_path}")
        import fitz  # PyMuPDF
        
        # 打开PDF文件
        pdf_document = fitz.open(pdf_path)
        image_paths = []
        
        print(f"PDF文档共 {len(pdf_document)} 页")
        
        for page_num in range(len(pdf_document)):
            # 获取页面
            page = pdf_document.load_page(page_num)
            
            # 设置缩放比例（影响图片质量）
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)
            
            # 保存图片
            image_filename = f"page_{page_num + 1:03d}.png"
            image_path = os.path.join(output_dir, image_filename)
            pix.save(image_path)
            image_paths.append(image_path)
            print(f"  保存第 {page_num + 1} 页: {image_filename}")
        
        pdf_document.close()
        return image_paths
        
    except ImportError:
        print("✗ PyMuPDF库未安装")
        return []
    except Exception as e:
        print(f"✗ PDF转换失败: {e}")
        return []

def test_file_detection():
    """测试文件类型检测功能"""
    print("=" * 50)
    print("测试文件类型检测功能")
    print("=" * 50)
    
    test_cases = [
        "http://example.com/document.pdf",
        "https://example.com/image.jpg",
        "C:\\Users\\<USER>\\document.pdf",
        "/home/<USER>/image.png",
        "document.txt",
        "unknown_file"
    ]
    
    for test_url in test_cases:
        file_type = detect_file_type_test(test_url)
        print(f"{test_url} -> {file_type}")

def detect_file_type_test(file_path_or_url):
    """简化的文件类型检测"""
    try:
        from urllib.parse import urlparse
        from pathlib import Path
        import re
        
        # 提取文件扩展名
        if file_path_or_url.startswith(('http://', 'https://')):
            # URL路径
            parsed_url = urlparse(file_path_or_url)
            file_path = parsed_url.path
        else:
            # 本地路径
            file_path = file_path_or_url
        
        # 获取文件扩展名
        file_extension = Path(file_path).suffix.lower()
        
        # 根据扩展名判断文件类型
        if file_extension == '.pdf':
            return 'pdf'
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            return 'image'
        elif file_extension in ['.txt', '.md', '.doc', '.docx']:
            return 'text'
        else:
            # 尝试通过URL模式匹配
            if re.search(r'\.pdf(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'pdf'
            elif re.search(r'\.(jpg|jpeg|png|bmp|gif|tiff|webp)(\?|$)', file_path_or_url, re.IGNORECASE):
                return 'image'
            else:
                return 'unknown'
                
    except Exception as e:
        print(f"文件类型检测失败: {e}")
        return 'unknown'

if __name__ == "__main__":
    print("PDF处理功能独立测试")
    print("测试时间:", __import__('time').strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试文件类型检测
    test_file_detection()
    
    print()
    
    # 测试PDF下载和转换
    success = test_pdf_download_and_convert()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 PDF处理功能测试成功！")
    else:
        print("❌ PDF处理功能测试失败！")
    print("=" * 50)
